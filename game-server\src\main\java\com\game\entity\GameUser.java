package com.game.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 游戏用户实体
 */
@Data
@Entity
@Table(name = "game_user")
public class GameUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名
     */
    @Column(unique = true, nullable = false)
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 游戏币余额
     */
    @Column(precision = 15, scale = 2)
    private BigDecimal coinBalance = BigDecimal.ZERO;

    /**
     * 累计充值金额
     */
    @Column(precision = 15, scale = 2)
    private BigDecimal totalRecharge = BigDecimal.ZERO;

    /**
     * 用户状态 1-正常 0-禁用
     */
    private Integer status = 1;

    /**
     * 创建时间
     */
    @CreationTimestamp
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updateTime;
}