#################################
# spring boot支持外部application.yml  读取优先级为：
#   1、file:./config/（当前目录下的config文件夹）
#   2、file:./（当前目录）
#   3、classpath:/config/（classpath下的config目录）
#   4、classpath:/（classpath根目录）
#   建议： 如果是jar则放置到与jar相同的目录下，  如果解压文件放置到classpath: config目录下。 (需要将文件重命名为 application.yml )
#
#   该yml文件只配置与环境相关的参数， 其他配置读取项目下的配置项
#
#################################
server:
  port: 9216 #设置端口
  servlet:
    context-path: / #设置应用的目录.  前缀需要带/, 无需设置后缀, 示例 【 /xxx 】 or 【 / 】
spring:
  servlet:
    multipart:
      enabled: true #是否启用http上传处理
      max-request-size: 10MB #最大请求文件的大小
      max-file-size: 10MB #单个文件最大大小

# 日志配置
logging:
  path: ./logs/payment  # 日志文件存储路径
  level:
    root: info
    com.unipay: debug
  file:
    path: ./logs  #日志存放地址

# knife4j APIDOC文档
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      display-name: '支付网关'
      paths-to-match: '/**'
      packages-to-scan: com.unipay.pay.ctrl.test
knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 公共响应
  documents:
    - name: API
      locations: classpath:markdown/doc/*
      group: default

#系统业务参数
isys:

  #是否允许跨域请求 [生产环境建议关闭， 若api与前端项目没有在同一个域名下时，应开启此配置或在nginx统一配置允许跨域]
  allow-cors: true

  #是否内存缓存配置信息: true表示开启如支付网关地址/商户应用配置/服务商配置等， 开启后需检查MQ的广播模式是否正常； false表示直接查询DB.
  cache-config: false

  oss:
    file-root-path: /jeepayhomes/service/uploads #存储根路径 ( 无需以‘/’结尾 )
    file-public-path: ${isys.oss.file-root-path}/public #公共读取块  ( 一般配合root-path参数进行设置，需以‘/’ 开头, 无需以‘/’结尾 )
    file-private-path: ${isys.oss.file-root-path}/private #私有化本地访问，不允许url方式公共读取 ( 一般配合root-path参数进行设置，需以‘/’ 开头, 无需以‘/’结尾  )

    # [local]: 本地存储，所有的文件将存在放本地，可通过nfs, rsync工具实现多机共享；
    # [aliyun-oss]: 将文件统一上传到阿里云oss服务器;  注意：需调整jeepay-components-oss/pom.xml中的aliyun-sdk-oss组件依赖方式
    service-type: local

    # 阿里云OSS服务配置信息
    aliyun-oss:
      endpoint: oss-cn-beijing.aliyuncs.com  #endpoint  如： oss-cn-beijing.aliyuncs.com
      public-bucket-name: bucket1 #公共读 桶名称
      private-bucket-name: bucket2  #私有 桶名称
      access-key-id: KEY_KEY_KEY   #AccessKeyId
      access-key-secret: SECRET_SECRET_SECRET  #AccessKeySecret

  mq:
    vender: activeMQ  #  切换MQ厂商， 支持：【 activeMQ  rabbitMQ  rocketMQ  aliYunRocketMQ 】， 需正确配置 【对应的yml参数】 和 【jeepay-components-mq项目下pom.xml中的依赖包】。

