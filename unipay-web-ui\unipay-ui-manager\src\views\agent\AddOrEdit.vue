<template>
    <a-drawer
      v-model:open="vdata.open"
      :mask-closable="false"
      :title="vdata.isAdd ? '新增代理商' : '修改代理商'"
      :body-style="{ paddingBottom: '80px' }"
      width="40%"
      class="drawer-width"
      @close="onClose"
    >
      <a-form
        ref="infoFormModel"
        :model="vdata.saveObject"
        layout="vertical"
        :rules="rules"
        name="basic"
      >
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="代理商名称" name="agentName">
              <a-input v-model:value="vdata.saveObject.agentName" placeholder="请输入代理商名称" />
            </a-form-item>
          </a-col>
          <a-col :span="10" v-if="!vdata.isAdd">
            <a-form-item label="代理商号" name="agentNo">
              <a-input
                v-model:value="vdata.saveObject.agentNo"
                placeholder="系统自动生成"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="代理商简称" name="agentShortName">
              <a-input v-model:value="vdata.saveObject.agentShortName" placeholder="请输入代理商简称" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="上级代理商" name="parentAgentNo">
              <a-select
                v-model:value="vdata.saveObject.parentAgentNo"
                placeholder="请选择上级代理商（可选）"
                allow-clear
              >
                <a-select-option v-for="agent in vdata.agentList" :key="agent.agentNo" :value="agent.agentNo">
                  {{ agent.agentName + ' [ ' + agent.agentNo + ' ]' }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="联系人姓名" name="contactName">
              <a-input v-model:value="vdata.saveObject.contactName" placeholder="请输入联系人姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="联系人手机号" name="contactTel">
              <a-input v-model:value="vdata.saveObject.contactTel" placeholder="请输入联系人手机号" />
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="联系人邮箱" name="contactEmail">
              <a-input v-model:value="vdata.saveObject.contactEmail" placeholder="请输入联系人邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="分润比例" name="profitRate">
              <a-input-number
                v-model:value="vdata.saveObject.profitRate"
                placeholder="请输入分润比例"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="4"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">
                请输入0-1之间的小数，如0.05表示5%
              </div>
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="省份" name="province">
              <a-input v-model:value="vdata.saveObject.province" placeholder="请输入省份" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="城市" name="city">
              <a-input v-model:value="vdata.saveObject.city" placeholder="请输入城市" />
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="10">
            <a-form-item label="区县" name="district">
              <a-input v-model:value="vdata.saveObject.district" placeholder="请输入区县" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="状态" name="state">
              <a-radio-group v-model:value="vdata.saveObject.state">
                <a-radio :value="1">正常</a-radio>
                <a-radio :value="0">停用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="24">
            <a-form-item label="详细地址" name="address">
              <a-input v-model:value="vdata.saveObject.address" placeholder="请输入详细地址" />
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex">
          <a-col :span="24">
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="vdata.saveObject.remark" placeholder="请输入备注" />
            </a-form-item>
          </a-col>
        </a-row>
  
        <!-- 登录账户信息 -->
        <a-row justify="space-between" type="flex" v-if="vdata.isAdd">
          <a-col :span="24">
            <a-divider orientation="left">
              <a-tag color="#1890ff">登录账户信息</a-tag>
            </a-divider>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex" v-if="vdata.isAdd">
          <a-col :span="10">
            <a-form-item label="登录用户名" name="loginUsername">
              <a-input v-model:value="vdata.saveObject.loginUsername" placeholder="请输入登录用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="登录密码" name="loginPassword">
              <a-input-password
                v-model:value="vdata.saveObject.loginPassword"
                placeholder="请输入登录密码"
                autocomplete="new-password"
              />
            </a-form-item>
          </a-col>
        </a-row>
  
        <a-row justify="space-between" type="flex" v-if="vdata.isAdd">
          <a-col :span="10">
            <a-form-item label="确认密码" name="confirmPassword">
              <a-input-password
                v-model:value="vdata.saveObject.confirmPassword"
                placeholder="请再次输入密码"
                autocomplete="new-password"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="真实姓名" name="realname">
              <a-input v-model:value="vdata.saveObject.realname" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
        </a-row>
  
        <!-- 重置密码板块 -->
        <a-row justify="space-between" type="flex">
          <a-col :span="24">
            <a-divider v-if="vdata.resetIsShow" orientation="left">
              <a-tag color="#FF4B33">账户安全</a-tag>
            </a-divider>
          </a-col>
        </a-row>
        <div>
          <a-row justify="space-between" type="flex">
            <a-col :span="10">
              <a-form-item v-if="vdata.resetIsShow" label="">
                重置密码：
                <a-checkbox v-model:checked="vdata.sysPassword.resetPass" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item v-if="vdata.sysPassword.resetPass" label="">
                恢复默认密码：
                <a-checkbox v-model:checked="vdata.sysPassword.defaultPass" @click="isResetPass" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
  
        <div v-if="vdata.sysPassword.resetPass">
          <div v-show="!vdata.sysPassword.defaultPass">
            <a-row justify="space-between" type="flex">
              <a-col :span="10">
                <a-form-item label="新密码：" name="newPwd">
                  <a-input-password
                    v-model:value="vdata.newPwd"
                    autocomplete="new-password"
                    :disabled="vdata.sysPassword.defaultPass"
                  />
                </a-form-item>
              </a-col>
  
              <a-col :span="10">
                <a-form-item label="确认新密码：" name="confirmPwd">
                  <a-input-password
                    v-model:value="vdata.sysPassword.confirmPwd"
                    autocomplete="new-password"
                    :disabled="vdata.sysPassword.defaultPass"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form>
      <div class="drawer-btn-center">
        <a-button :style="{ marginRight: '8px' }" style="margin-right: 8px" @click="onClose">
          取消
        </a-button>
        <a-button type="primary" :loading="vdata.btnLoading" @click="handleOkFunc">保存</a-button>
      </div>
    </a-drawer>
  </template>
  
  <script setup lang="ts">
  import { API_URL_AGENT_LIST, req } from '@/api/manage'
  import { Base64 } from 'js-base64'
  import { reactive, ref, getCurrentInstance } from 'vue'
  const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties
  
  const props: any = defineProps({
    callbackFunc: { type: Function },
  })
  
  const infoFormModel = ref()
  
  const vdata: any = reactive({
    newPwd: '', //  新密码
    resetIsShow: false, // 重置密码是否展现
    sysPassword: {
      resetPass: false, // 重置密码
      defaultPass: true, // 使用默认密码
      confirmPwd: '', //  确认密码
    },
    btnLoading: false,
    isAdd: true, // 新增 or 修改页面标志
    saveObject: {}, // 数据对象
    recordId: null, // 更新对象ID
    open: false, // 是否显示弹层/抽屉
    agentList: [], // 代理商下拉列表
  })
  
  const rules: any = reactive({
    agentName: [{ required: true, message: '请输入代理商名称', trigger: 'blur' }],
    agentShortName: [{ required: true, message: '请输入代理商简称', trigger: 'blur' }],
    contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    contactEmail: [
      {
        required: false,
        pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
        message: '请输入正确的邮箱地址',
        trigger: 'blur',
      },
    ],
    contactTel: [
      { required: true, pattern: /^1\d{10}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    profitRate: [
      {
        required: false,
        validator: (rule, value) => {
          if (value !== null && value !== undefined && (value < 0 || value > 1)) {
            return Promise.reject('分润比例必须在0-1之间')
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    loginUsername: [
      { required: true, message: '请输入登录用户名', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z0-9_]{4,20}$/,
        message: '用户名必须是4-20位字母、数字或下划线',
        trigger: 'blur',
      },
    ],
    loginPassword: [
      { required: true, message: '请输入登录密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度必须在6-20位之间', trigger: 'blur' },
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (rule, value) => {
          if (value !== vdata.saveObject.loginPassword) {
            return Promise.reject('两次输入的密码不一致')
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    realname: [
      { required: true, message: '请输入真实姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '真实姓名长度必须在2-20位之间', trigger: 'blur' },
    ],
    newPwd: [
      { required: false, trigger: 'blur' },
      {
        validator: (rule, value) => {
          if (!vdata.sysPassword.defaultPass) {
            if (vdata.newPwd.length < 6 || vdata.newPwd.length > 12) {
              return Promise.reject('请输入6-12位新密码')
            }
          }
          return Promise.resolve()
        },
      },
    ], // 新密码
    confirmPwd: [
      { required: false, trigger: 'blur' },
      {
        validator: (rule, value) => {
          if (!vdata.sysPassword.defaultPass) {
            if (vdata.newPwd === vdata.sysPassword.confirmPwd) {
              return Promise.resolve()
            } else {
              return Promise.reject('新密码与确认密码不一致')
            }
          } else {
            return Promise.resolve()
          }
        },
      },
    ], // 确认新密码
  })
  
  function show(recordId) {
    // 弹层打开事件
    vdata.isAdd = !recordId
  
    vdata.saveObject = { state: 1 } // 数据清空
    if (infoFormModel.value) {
      infoFormModel.value.resetFields()
    }
    
    // 加载代理商下拉列表
    req.list(API_URL_AGENT_LIST, { pageSize: -1, state: 1 }).then((res) => {
      vdata.agentList = res.records || []
    })
    
    if (!vdata.isAdd) {
      // 修改信息 延迟展示弹层
      vdata.resetIsShow = true // 展示重置密码板块
      vdata.recordId = recordId
  
      req.getById(API_URL_AGENT_LIST, recordId).then((res) => {
        vdata.saveObject = res
      })
      vdata.open = true
    } else {
      vdata.open = true // 立马展示弹层信息
    }
  }
  
  function handleOkFunc() {
    // 点击【确认】按钮事件
    infoFormModel.value.validate().then((valid) => {
      if (valid) {
        // 验证通过
        // 请求接口
        if (vdata.isAdd) {
          vdata.btnLoading = true
          req
            .add(API_URL_AGENT_LIST, vdata.saveObject)
            .then((res) => {
              $infoBox.message.success('新增成功')
              vdata.open = false
              props.callbackFunc() // 刷新列表
              vdata.btnLoading = false
            })
            .catch((res) => {
              vdata.btnLoading = false
            })
        } else {
          vdata.sysPassword.confirmPwd = Base64.encode(vdata.sysPassword.confirmPwd)
          Object.assign(vdata.saveObject, vdata.sysPassword) // 拼接对象
  
          req
            .updateById(API_URL_AGENT_LIST, vdata.recordId, vdata.saveObject)
            .then((res) => {
              $infoBox.message.success('修改成功')
              vdata.open = false
              props.callbackFunc() // 刷新列表
              vdata.btnLoading = false
              vdata.resetIsShow = true // 展示重置密码板块
              vdata.sysPassword.resetPass = false
              vdata.sysPassword.defaultPass = true // 是否使用默认密码默认为true
              resetPassEmpty() // 清空密码
            })
            .catch((res) => {
              vdata.btnLoading = false
              vdata.resetIsShow = true // 展示重置密码板块
              vdata.sysPassword.resetPass = false
              vdata.sysPassword.defaultPass = true // 是否使用默认密码默认为true
              resetPassEmpty() // 清空密码
            })
        }
      }
    })
  }
  
  function onClose() {
    vdata.open = false
    vdata.resetIsShow = false // 取消重置密码板块展示
    vdata.sysPassword.resetPass = false
    resetPassEmpty()
    vdata.sysPassword.defaultPass = true // 是否使用默认密码默认为true
  }
  
  // 使用默认密码重置是否为true
  function isResetPass() {
    if (!vdata.sysPassword.defaultPass) {
      vdata.newPwd = ''
      vdata.sysPassword.confirmPwd = ''
    }
  }
  
  // 保存后清空密码
  function resetPassEmpty() {
    vdata.newPwd = ''
    vdata.sysPassword.confirmPwd = ''
    vdata.open = false
  }
  
  defineExpose({ show })
  </script>
  