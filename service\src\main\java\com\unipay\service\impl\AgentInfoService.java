package com.unipay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.AgentInfo;
import com.unipay.core.entity.SysUser;
import com.unipay.core.entity.SysUserAuth;
import com.unipay.core.entity.SysUserRoleRela;
import com.unipay.core.exception.BizException;
import com.unipay.core.utils.StringKit;
import com.unipay.service.mapper.AgentInfoMapper;
import com.unipay.service.impl.SysUserService;
import com.unipay.service.impl.SysUserAuthService;
import com.unipay.service.impl.SysUserRoleRelaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 代理商信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AgentInfoService extends ServiceImpl<AgentInfoMapper, AgentInfo> {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserAuthService sysUserAuthService;

    @Autowired
    private SysUserRoleRelaService sysUserRoleRelaService;

    /**
     * 根据代理商号查询代理商信息
     */
    public AgentInfo getByAgentNo(String agentNo) {
        return getOne(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo));
    }

    /**
     * 创建代理商和系统用户
     * @param agentInfo 代理商信息
     * @param loginPassword 登录密码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createAgentWithUser(AgentInfo agentInfo, String loginPassword) {
        try {
            // 构建代理商路径
            String agentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
            agentInfo.setAgentPath(agentPath);
            
            // 设置代理商层级
            if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
                agentInfo.setAgentLevel((byte) 1);
            } else {
                AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
                if (parentAgent != null) {
                    agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
                }
            }
            
            // 设置默认状态
            if (agentInfo.getState() == null) {
                agentInfo.setState(AgentInfo.STATE_NORMAL);
            }
            
            // 保存代理商信息
            boolean agentResult = save(agentInfo);
            if (!agentResult) {
                throw new RuntimeException("创建代理商失败");
            }
            
            // 创建系统用户
            if (StringUtils.isNotBlank(loginPassword)) {
                // 使用反射获取loginUsername字段值
                String loginUsername = null;
                try {
                    java.lang.reflect.Field loginUsernameField = agentInfo.getClass().getDeclaredField("loginUsername");
                    loginUsernameField.setAccessible(true);
                    loginUsername = (String) loginUsernameField.get(agentInfo);
                } catch (Exception e) {
                    log.warn("获取loginUsername字段失败", e);
                }

                if (StringUtils.isNotBlank(loginUsername)) {
                    // 创建系统用户
                    SysUser sysUser = new SysUser();
                    sysUser.setLoginUsername(loginUsername);
                    sysUser.setRealname(agentInfo.getContactName());
                    sysUser.setTelphone(agentInfo.getContactTel());
                    sysUser.setUserNo(StringKit.getUUID(8));
                    sysUser.setSex(CS.SEX_MALE);
                    sysUser.setAvatarUrl("");
                    sysUser.setState((byte) CS.PUB_USABLE);
                    sysUser.setIsAdmin(CS.NO);
                    sysUser.setSysType(CS.SYS_TYPE.AGENT);
                    sysUser.setBelongInfoId(agentInfo.getAgentNo());
                    sysUser.setCreatedAt(new Date());
                    sysUser.setUpdatedAt(new Date());

                    boolean userResult = sysUserService.save(sysUser);
                    if (!userResult) {
                        throw new RuntimeException("创建系统用户失败");
                    }

                    // 创建用户认证信息
                    SysUserAuth sysUserAuth = new SysUserAuth();
                    sysUserAuth.setUserId(sysUser.getSysUserId());
                    sysUserAuth.setIdentityType(CS.AUTH_TYPE.LOGIN_USER_NAME);
                    sysUserAuth.setIdentifier(sysUser.getLoginUsername());
                    sysUserAuth.setCredential(new BCryptPasswordEncoder().encode(loginPassword));
                    sysUserAuth.setSalt("");
                    sysUserAuth.setSysType(CS.SYS_TYPE.AGENT);

                    sysUserAuthService.save(sysUserAuth);

                    // 分配代理商管理员角色
                    SysUserRoleRela sysUserRoleRela = new SysUserRoleRela();
                    sysUserRoleRela.setUserId(sysUser.getSysUserId());
                    sysUserRoleRela.setRoleId("ROLE_AGENT_ADMIN");

                    sysUserRoleRelaService.save(sysUserRoleRela);

                    // 更新代理商的初始用户ID
                    agentInfo.setInitUserId(sysUser.getSysUserId());
                    updateById(agentInfo);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("创建代理商和系统用户失败", e);
            throw e;
        }
    }

    /**
     * 分页查询代理商信息
     */
    public IPage<AgentInfo> selectPage(IPage<?> page, AgentInfo agentInfo, String currentAgentNo) {
        LambdaQueryWrapper<AgentInfo> wrapper = AgentInfo.gw();

        // 如果传入了当前代理商号，则只查询其下级代理商
        if (StringUtils.isNotBlank(currentAgentNo)) {
            wrapper.eq(AgentInfo::getParentAgentNo, currentAgentNo);
        }

        if (agentInfo != null) {
            // 代理商名称模糊查询
            if (StringUtils.isNotBlank(agentInfo.getAgentName())) {
                wrapper.like(AgentInfo::getAgentName, agentInfo.getAgentName());
            }
            // 代理商号精确查询
            if (StringUtils.isNotBlank(agentInfo.getAgentNo())) {
                wrapper.eq(AgentInfo::getAgentNo, agentInfo.getAgentNo());
            }
            // 状态查询
            if (agentInfo.getState() != null) {
                wrapper.eq(AgentInfo::getState, agentInfo.getState());
            }
            // 代理商层级查询
            if (agentInfo.getAgentLevel() != null) {
                wrapper.eq(AgentInfo::getAgentLevel, agentInfo.getAgentLevel());
            }
            // 联系电话查询
            if (StringUtils.isNotBlank(agentInfo.getContactTel())) {
                wrapper.like(AgentInfo::getContactTel, agentInfo.getContactTel());
            }
            // 如果查询条件中指定了上级代理商号，则覆盖currentAgentNo的过滤
            if (StringUtils.isNotBlank(agentInfo.getParentAgentNo())) {
                wrapper.eq(AgentInfo::getParentAgentNo, agentInfo.getParentAgentNo());
            }
        }

        wrapper.orderByDesc(AgentInfo::getCreatedAt);
        return page(Page.of(page.getCurrent(), page.getSize()), wrapper);
    }

    /**
     * 分页查询代理商信息（管理端使用，不过滤上级代理商）
     */
    public IPage<AgentInfo> selectPage(IPage<?> page, AgentInfo agentInfo) {
        return selectPage(page, agentInfo, null);
    }

    /**
     * 检查代理商号是否存在
     */
    public boolean isExistAgentNo(String agentNo) {
        return count(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo)) > 0;
    }

    /**
     * 更新代理商信息
     */
    public boolean updateAgent(AgentInfo agentInfo) {
        agentInfo.setUpdatedAt(new Date());
        return updateById(agentInfo);
    }

    /**
     * 创建代理商（简单版本，用于管理端）
     */
    public boolean createAgent(AgentInfo agentInfo) {
        try {
            // 构建代理商路径
            String agentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
            agentInfo.setAgentPath(agentPath);
            
            // 设置代理商层级
            if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
                agentInfo.setAgentLevel((byte) 1);
            } else {
                AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
                if (parentAgent != null) {
                    agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
                }
            }
            
            // 设置默认状态
            if (agentInfo.getState() == null) {
                agentInfo.setState(AgentInfo.STATE_NORMAL);
            }
            
            return save(agentInfo);
        } catch (Exception e) {
            log.error("创建代理商失败", e);
            return false;
        }
    }

    /**
     * 检查是否可以删除代理商
     */
    public boolean canDeleteAgent(String agentNo) {
        // 检查是否有下级代理商
        long subAgentCount = count(AgentInfo.gw().eq(AgentInfo::getParentAgentNo, agentNo));
        return subAgentCount == 0;
    }

    /**
     * 构建代理商路径
     */
    private String buildAgentPath(String parentAgentNo, String agentNo) {
        if (StringUtils.isBlank(parentAgentNo)) {
            return "/" + agentNo;
        }
        
        AgentInfo parentAgent = getByAgentNo(parentAgentNo);
        if (parentAgent == null) {
            throw new BizException("上级代理商不存在");
        }
        
        return parentAgent.getAgentPath() + "/" + agentNo;
    }
}