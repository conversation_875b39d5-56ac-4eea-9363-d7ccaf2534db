package com.game.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值订单实体
 */
@Data
@Entity
@Table(name = "recharge_order")
public class RechargeOrder {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 游戏订单号
     */
    @Column(unique = true, nullable = false)
    private String gameOrderNo;

    /**
     * 支付网关订单号
     */
    private String payOrderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 充值金额（元）
     */
    @Column(precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 获得游戏币数量
     */
    @Column(precision = 15, scale = 2)
    private BigDecimal coinAmount;

    /**
     * 订单状态
     * 0-待支付 1-支付成功 2-支付失败 3-已取消
     */
    private Integer status = 0;

    /**
     * 支付方式
     */
    private String payWay;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 支付网关回调数据
     */
    @Column(columnDefinition = "TEXT")
    private String callbackData;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @CreationTimestamp
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updateTime;

    // 订单状态常量
    public static final int STATUS_PENDING = 0;   // 待支付
    public static final int STATUS_SUCCESS = 1;   // 支付成功
    public static final int STATUS_FAILED = 2;    // 支付失败
    public static final int STATUS_CANCELLED = 3; // 已取消
}