
package com.unipay.mgr.ctrl.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unipay.core.aop.MethodLog;
import com.unipay.core.constants.ApiCodeEnum;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.AgentInfo;
import com.unipay.core.entity.SysUser;
import com.unipay.core.entity.SysUserAuth;
import com.unipay.core.entity.SysUserRoleRela;
import com.unipay.core.model.ApiPageRes;
import com.unipay.core.model.ApiRes;
import com.unipay.core.utils.StringKit;
import com.unipay.mgr.ctrl.CommonCtrl;
import com.unipay.service.impl.AgentInfoService;
import com.unipay.service.impl.SysUserAuthService;
import com.unipay.service.impl.SysUserRoleRelaService;
import com.unipay.service.impl.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 代理商信息管理类
 *
 * <AUTHOR> 
 * @date 2024-01-01
 */
@Tag(name = "代理商管理（运营平台）")
@RestController
@RequestMapping("/api/agentInfo")
public class AgentInfoController extends CommonCtrl {

    @Autowired private AgentInfoService agentInfoService;
    @Autowired private SysUserService sysUserService;
    @Autowired private SysUserAuthService sysUserAuthService;
    @Autowired private SysUserRoleRelaService sysUserRoleRelaService;

    /**
  
     * @date 2024-01-01
     */
    @Operation(summary = "代理商信息列表")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "pageNumber", description = "分页页码"),
            @Parameter(name = "pageSize", description = "分页条数（-1时查全部数据）"),
            @Parameter(name = "agentNo", description = "代理商号"),
            @Parameter(name = "agentName", description = "代理商名称"),
            @Parameter(name = "state", description = "状态: 0-停用, 1-正常")
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_LIST')")
    @GetMapping
    public ApiPageRes<AgentInfo> list() {

        AgentInfo queryObject = getObject(AgentInfo.class);

        IPage<AgentInfo> pages = agentInfoService.selectPage(getIPage(), queryObject);

        return ApiPageRes.pages(pages);
    }

    /**
  
     * @date 2024-01-01
     */
    @Operation(summary = "代理商详情")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_VIEW')")
    @GetMapping("/{agentNo}")
    public ApiRes<AgentInfo> detail(@PathVariable("agentNo") String agentNo) {
        AgentInfo agentInfo = agentInfoService.getByAgentNo(agentNo);
        if (agentInfo == null) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_SELETE, "该代理商不存在");
        }
        return ApiRes.ok(agentInfo);
    }

    /**
  
     * @date 2024-01-01
     */
    @Operation(summary = "新增代理商")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_ADD')")
    @MethodLog(remark = "新增代理商")
    @PostMapping
    public ApiRes<AgentInfo> add() {
        AgentInfo agentInfo = getObject(AgentInfo.class);

        // 获取前端传递的登录用户信息
        String loginUsername = getValString("loginUsername");
        String loginPassword = getValString("loginPassword");
        String realname = getValString("realname");

        // 校验代理商号是否重复
        if (agentInfoService.isExistAgentNo(agentInfo.getAgentNo())) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE, "代理商号已存在");
        }

        // 校验登录用户名是否重复
        if (StringUtils.isNotEmpty(loginUsername)) {
            if (sysUserService.count(SysUser.gw().eq(SysUser::getSysType, CS.SYS_TYPE.AGENT).eq(SysUser::getLoginUsername, loginUsername)) > 0) {
                return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE, "登录用户名已存在");
            }
        }

        // 设置创建信息
        SysUser currentUser = getCurrentUser().getSysUser();
        agentInfo.setCreatedUid(currentUser.getSysUserId());
        agentInfo.setCreatedBy(currentUser.getRealname());
        agentInfo.setCreatedAt(new Date());
        agentInfo.setUpdatedAt(new Date());

        // 默认状态为正常
        if (agentInfo.getState() == null) {
            agentInfo.setState(AgentInfo.STATE_NORMAL);
        }

        boolean result = agentInfoService.createAgent(agentInfo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE, "新增失败");
        }

        // 创建代理商管理员用户
        if (StringUtils.isNotEmpty(loginUsername) && StringUtils.isNotEmpty(loginPassword)) {
            createAgentAdminUser(agentInfo, loginUsername, loginPassword, realname);
        }

        return ApiRes.ok(agentInfo);
    }

    /**
  
     * @date 2024-01-01
     */
    @Operation(summary = "更新代理商信息")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_EDIT')")
    @MethodLog(remark = "更新代理商信息")
    @PutMapping("/{agentNo}")
    public ApiRes<AgentInfo> update(@PathVariable("agentNo") String agentNo) {
        AgentInfo agentInfo = getObject(AgentInfo.class);
        agentInfo.setAgentNo(agentNo);
        agentInfo.setUpdatedAt(new Date());

        boolean result = agentInfoService.updateAgent(agentInfo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_UPDATE, "更新失败");
        }
        return ApiRes.ok(agentInfo);
    }

    /**
  
     * @date 2024-01-01
     */
    @Operation(summary = "删除代理商")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "agentNo", description = "代理商号", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_AGENT_INFO_DELETE')")
    @MethodLog(remark = "删除代理商")
    @DeleteMapping("/{agentNo}")
    public ApiRes<?> delete(@PathVariable("agentNo") String agentNo) {
        
        // 检查是否可以删除
        if (!agentInfoService.canDeleteAgent(agentNo)) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_DELETE, "该代理商下存在下级代理商或关联商户，无法删除");
        }

        boolean result = agentInfoService.removeById(agentNo);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_DELETE, "删除失败");
        }

        // 删除代理商相关用户
        // TODO: 实现删除代理商相关用户的逻辑

        return ApiRes.ok();
    }

    /**
     * 创建代理商管理员用户
     */
    private void createAgentAdminUser(AgentInfo agentInfo, String loginUsername, String loginPassword, String realname) {
        // 创建系统用户
        SysUser sysUser = new SysUser();
        sysUser.setLoginUsername(loginUsername);
        sysUser.setRealname(StringUtils.isNotEmpty(realname) ? realname : agentInfo.getAgentName() + "管理员");
        sysUser.setTelphone(agentInfo.getContactTel());
        sysUser.setUserNo(StringKit.getUUID(8));
        sysUser.setSex(CS.SEX_MALE);
        sysUser.setAvatarUrl("");
        sysUser.setState((byte) CS.PUB_USABLE);
        sysUser.setIsAdmin(CS.NO);
        sysUser.setSysType(CS.SYS_TYPE.AGENT);
        sysUser.setBelongInfoId(agentInfo.getAgentNo());
        sysUser.setCreatedAt(new Date());
        sysUser.setUpdatedAt(new Date());

        sysUserService.save(sysUser);

        // 创建用户认证信息
        SysUserAuth sysUserAuth = new SysUserAuth();
        sysUserAuth.setUserId(sysUser.getSysUserId());
        sysUserAuth.setIdentityType(CS.AUTH_TYPE.LOGIN_USER_NAME);
        sysUserAuth.setIdentifier(sysUser.getLoginUsername());
        sysUserAuth.setCredential(new BCryptPasswordEncoder().encode(loginPassword)); // 使用自定义密码
        sysUserAuth.setSalt("");
        sysUserAuth.setSysType(CS.SYS_TYPE.AGENT);

        sysUserAuthService.save(sysUserAuth);

        // 分配代理商管理员角色
        SysUserRoleRela sysUserRoleRela = new SysUserRoleRela();
        sysUserRoleRela.setUserId(sysUser.getSysUserId());
        sysUserRoleRela.setRoleId("ROLE_AGENT_ADMIN");

        sysUserRoleRelaService.save(sysUserRoleRela);

        // 更新代理商的初始用户ID
        agentInfo.setInitUserId(sysUser.getSysUserId());
        agentInfoService.updateById(agentInfo);
    }

}
