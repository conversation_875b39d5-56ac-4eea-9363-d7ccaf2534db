/*
 *  全系列 restful api格式, 定义通用req对象
 *
 *  <AUTHOR>
 *  @site https://www.unipay.com
 *  @date 2021/5/8 07:18
 */

import request from '@/http/request'

export const req = {

  // 通用列表查询接口
  list: (url, params) => {
    return request.request({ url: url, method: 'GET', params: params }, true, true, false)
  },

  // 通用新增接口
  add: (url, data) => {
    return request.request({ url: url, method: 'POST', data: data }, true, true, false)
  },

  // 通用查询单条数据接口
  getById: (url, bizId) => {
    return request.request({ url: url + '/' + bizId, method: 'GET' }, true, true, false)
  },

  // 通用修改接口
  updateById: (url, bizId, data) => {
    return request.request({ url: url + '/' + bizId, method: 'PUT', data: data }, true, true, false)
  },

  // 通用删除接口
  delById: (url, bizId) => {
    return request.request({ url: url + '/' + bizId, method: 'DELETE' }, true, true, false)
  }
}

// 全系列 restful api格式 (全局loading方式)
export const reqLoad = {

  // 通用列表查询接口
  list: (url, params) => {
    return request.request({ url: url, method: 'GET', params: params }, true, true, true)
  },

  // 通用新增接口
  add: (url, data) => {
    return request.request({ url: url, method: 'POST', data: data }, true, true, true)
  },

  // 通用查询单条数据接口
  getById: (url, bizId) => {
    return request.request({ url: url + '/' + bizId, method: 'GET' }, true, true, true)
  },

  // 通用修改接口
  updateById: (url, bizId, data) => {
    return request.request({ url: url + '/' + bizId, method: 'PUT', data: data }, true, true, true)
  },

  // 通用删除接口
  delById: (url, bizId) => {
    return request.request({ url: url + '/' + bizId, method: 'DELETE' }, true, true, true)
  }
}

/** 角色管理页面 **/
export const API_URL_ENT_LIST = '/api/sysEnts'
export const API_URL_ROLE_LIST = '/api/sysRoles'
export const API_URL_ROLE_ENT_RELA_LIST = '/api/sysRoleEntRelas'
export const API_URL_SYS_USER_LIST = '/api/sysUsers'
export const API_URL_USER_ROLE_RELA_LIST = '/api/sysUserRoleRelas'
/** 首页统计 **/
export const API_URL_MAIN_STATISTIC = 'api/mainChart'

/** 商户管理 **/
export const API_URL_MCH_LIST = '/api/mchInfo'
/** 商户应用管理 **/
export const API_URL_MCH_APP = '/api/mchApps'
/** 代理商管理 **/
export const API_URL_AGENT_LIST = '/api/agentInfo'
/** 支付订单管理 **/
export const API_URL_PAY_ORDER_LIST = '/api/payOrder'
/** 退款订单管理 **/
export const API_URL_REFUND_ORDER_LIST = '/api/refundOrder'
/** 支付方式列表 **/
export const API_URL_PAYWAYS_LIST = '/api/payWays'
/** 商户支付参数配置 **/
export const API_URL_MCH_PAYCONFIGS_LIST = '/api/mch/payConfigs'
/** 商户支付通道配置 **/
export const API_URL_MCH_PAYPASSAGE_LIST = '/api/mch/payPassages'
/** 转账订单管理 **/
export const API_URL_TRANSFER_ORDER_LIST = '/api/transferOrders'

/** 分账接收者账号组管理 **/
export const API_URL_DIVISION_RECEIVER_GROUP = '/api/divisionReceiverGroups'
/** 分账接收者账号管理 **/
export const API_URL_DIVISION_RECEIVER = '/api/divisionReceivers'
/** 分账记录管理 **/
export const API_URL_PAY_ORDER_DIVISION_RECORD_LIST = '/api/division/records'

/** 上传图片/文件地址 **/
export const upload = {
  avatar: request.baseUrl + '/api/ossFiles/avatar',
  cert: request.baseUrl + '/api/ossFiles/cert'
}

const api = {
  user: '/user',
  role_list: '/role',
  service: '/service',
  permission: '/permission',
  permissionNoPager: '/permission/no-pager',
  orgTree: '/org/tree'
}

export default api

/** 获取权限树状结构图 **/
export function getEntTree (sysType) {
  return request.request({ url: '/api/sysEnts/showTree?sysType=' + sysType, method: 'GET' })
}

/** 更新用户角色信息 */
export function uSysUserRoleRela (sysUserId, roleIdList) {
  return request.request({
    url: 'api/sysUserRoleRelas/relas/' + sysUserId,
    method: 'POST',
    data: { roleIdListStr: JSON.stringify(roleIdList) }
  })
}

/** 获取支付金额周统计 **/
export function getPayAmountWeek () {
  return request.request({
    url: API_URL_MAIN_STATISTIC + '/payAmountWeek',
    method: 'GET'
  })
}

/** 获取数量统计 **/
export function getNumCount () {
  return request.request({
    url: API_URL_MAIN_STATISTIC + '/numCount',
    method: 'GET'
  })
}

/** 获取支付统计 **/
export function getPayCount (parameter) {
  return request.request({
    url: API_URL_MAIN_STATISTIC + '/payCount',
    method: 'GET',
    params: parameter
  })
}

/** 获取支付类型统计 **/
export function getPayType (parameter) {
  return request.request({
    url: API_URL_MAIN_STATISTIC + '/payTypeCount',
    method: 'GET',
    params: parameter
  })
}

/** 获取主页用户信息 **/
export function getMainUserInfo () {
  return request.request({
    url: API_URL_MAIN_STATISTIC,
    method: 'GET'
  })
}

/** 获取代理商和商户数量统计 **/
export function getAgentMchCount () {
  return request.request({
    url: API_URL_MAIN_STATISTIC + '/agentMchCount',
    method: 'GET'
  })
}

/** 更新用户密码 **/
export function updateUserPass (parameter) {
  return request.request({
    url: '/api/current/modifyPwd',
    method: 'put',
    data: parameter
  })
}

/** 更新用户信息 **/
export function updateUserInfo (parameter) {
  return request.request({
    url: '/api/current/user',
    method: 'put',
    data: parameter
  })
}

/** 获取用户信息 **/
export function getUserInfo () {
  return request.request({
    url: '/api/current/user',
    method: 'get'
  })
}

/** 退款接口 */
export function payOrderRefund (payOrderId, refundAmount, refundReason) {
  return request.request({
    url: '/api/payOrder/refunds/' + payOrderId,
    method: 'POST',
    data: { refundAmount, refundReason }
  })
}

/** 获取到webSocket的前缀 （ws://localhost:8080 || wss://localhost:8080）  **/
export function getWebSocketPrefix () {
  // 获取网站域名 +  端口号
  let domain = document.location.protocol + '//' + document.location.host

  // 判断api是否包含 /api字样
  let apiPrefix = process.env.VITE_APP_API_BASE_URL

  let wsPrefix = domain // websocket 前缀

  if (apiPrefix && apiPrefix !== '/') {
    wsPrefix = apiPrefix
  }

  // 替换http协议
  wsPrefix = wsPrefix.replace('https://', 'wss://').replace('http://', 'ws://')
  return wsPrefix
}

/** 数据导出 */
export function exportExcel (url, params, fileName) {
  return request.request({ url: url, method: 'GET', params: params, responseType: 'blob' }, true, true, true).then((res) => {
    if (!res) {
      return
    }

    if (res.size < 100) { // 异常情况的判断
      const reader = new FileReader()
      reader.readAsText(res, 'utf-8')
      reader.onload = function () {
        const response = JSON.parse(reader.result)
        this.$infoBox.message.error(response.msg)
      }
      return
    }

    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = fileName
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  })
}

/** 查询支付宝授权地址URL **/
export function queryAlipayIsvsubMchAuthUrl (mchAppId) {
  return request.request({ url: '/api/mch/payConfigs/alipayIsvsubMchAuthUrls/' + mchAppId, method: 'GET' })
}

/** 查询商户转账支出的接口 **/
export function queryMchTransferIfCode (appId) {
  return request.request({ url: '/api/mch/transferIfCodes/' + appId, method: 'GET' })
}

/** 发起转账请求的接口 **/
export function doTransferReq (data) {
  return request.request({ url: '/api/transferOrders', method: 'POST', data: data })
}

/** 支付测试 **/
export function payTest (data) {
  return request.request({ url: '/api/paytest', method: 'POST', data: data })
}

/** 查询支付方式 **/
export function queryPayWayList (appId) {
  return request.request({ url: '/api/mch/payWays/' + appId, method: 'GET' })
}

/** 查询商户对应应用下支付接口配置参数 **/
export function queryMchPayIfConfigList (appId) {
  return request.request({ url: '/api/mch/payConfigs/' + appId, method: 'GET' })
}

/** 更新商户对应应用下支付接口配置参数 **/
export function updateMchPayIfConfig (infoId, ifCode, data) {
  return request.request({ url: '/api/mch/payConfigs/' + infoId + '/' + ifCode, method: 'PUT', data: data })
}

/** 查询商户对应应用下支付通道配置 **/
export function queryMchPayPassageList (appId) {
  return request.request({ url: '/api/mch/payPassages/' + appId, method: 'GET' })
}

/** 更新商户对应应用下支付通道配置 **/
export function updateMchPayPassage (data) {
  return request.request({ url: '/api/mch/payPassages', method: 'POST', data: data })
}

/** 根据支付方式查询可用的支付接口 **/
export function queryPayIfCodeByPayway (wayCode) {
  return request.request({ url: '/api/mch/payPassages/availablePayInterface/' + wayCode, method: 'GET' })
}

/** 商户分账接收者账号组 列表 **/
export function queryMchDivisionReceiverGroupList (params) {
  return request.request({ url: '/api/divisionReceiverGroups', method: 'GET', params: params })
}

/** 商户分账接收者账号组 新增 **/
export function addMchDivisionReceiverGroup (data) {
  return request.request({ url: '/api/divisionReceiverGroups', method: 'POST', data: data })
}

/** 商户分账接收者账号组 删除 **/
export function delMchDivisionReceiverGroup (recordId) {
  return request.request({ url: '/api/divisionReceiverGroups/' + recordId, method: 'DELETE' })
}

/** 商户分账接收者账号组 修改 **/
export function updateMchDivisionReceiverGroup (recordId, data) {
  return request.request({ url: '/api/divisionReceiverGroups/' + recordId, method: 'PUT', data: data })
}

/** 商户分账接收者账号组 详情 **/
export function getMchDivisionReceiverGroup (recordId) {
  return request.request({ url: '/api/divisionReceiverGroups/' + recordId, method: 'GET' })
}

/** 商户分账接收者账号 列表 **/
export function queryMchDivisionReceiverList (params) {
  return request.request({ url: '/api/divisionReceivers', method: 'GET', params: params })
}

/** 商户分账接收者账号 新增 **/
export function addMchDivisionReceiver (data) {
  return request.request({ url: '/api/divisionReceivers', method: 'POST', data: data })
}

/** 商户分账接收者账号 删除 **/
export function delMchDivisionReceiver (recordId) {
  return request.request({ url: '/api/divisionReceivers/' + recordId, method: 'DELETE' })
}

/** 商户分账接收者账号 修改 **/
export function updateMchDivisionReceiver (recordId, data) {
  return request.request({ url: '/api/divisionReceivers/' + recordId, method: 'PUT', data: data })
}

/** 商户分账接收者账号 详情 **/
export function getMchDivisionReceiver (recordId) {
  return request.request({ url: '/api/divisionReceivers/' + recordId, method: 'GET' })
}

/** 分账记录 **/
export function queryPayOrderDivisionExecList (params) {
  return request.request({ url: '/api/division/records', method: 'GET', params: params })
}

/** 发起分账（手动分账） **/
export function divisionExec (payOrderId, useSysAutoDivisionReceivers, receivers) {
  return request.request({ url: '/api/division/exec/' + payOrderId, method: 'POST', data: { useSysAutoDivisionReceivers: useSysAutoDivisionReceivers, receivers: receivers } })
}

/** 查询商户分账接收者账号组 **/
export function queryMchDivisionReceiverGroupAll () {
  return request.request({ url: '/api/divisionReceiverGroups', method: 'GET', params: { pageSize: -1 } })
}

/** 查询商户分账接收者账号 **/
export function queryMchDivisionReceiverAll (groupId) {
  return request.request({ url: '/api/divisionReceivers', method: 'GET', params: { pageSize: -1, receiverGroupId: groupId } })
}

/** ========== 商户通知相关 ========== **/

/** 商户通知列表 **/
export const API_URL_MCH_NOTIFY_LIST = '/api/mchNotify'

/** 重发商户通知 **/
export function mchNotifyResend (notifyId) {
  return request.request({ url: '/api/mchNotify/resend/' + notifyId, method: 'POST' })
}
