# 代理商系统首页统计修复报告

## 问题描述

代理商系统进入首页后不显示属于代理商的商户的交易数据统计，点击首页后端也未收到任何请求。而运营平台和商户平台点击首页会正常接收到数据查询请求并能展示对应数据。

## 问题分析

通过代码分析发现，代理商系统首页数据统计无法正常工作的根本原因是：

1. **API路径混乱**：代理商系统存在两套主页统计控制器（`MainController`和`MainChartController`），前端调用的是`api/mainChart`路径，但权限配置使用的是不匹配的权限标识。

2. **权限配置不匹配**：`MainChartController`使用的是商户系统的权限（`ENT_MCH_MAIN_*`），而代理商系统应该使用`ENT_C_MAIN_*`权限，导致代理商用户无法访问。

3. **业务逻辑不适配**：原有的统计方法只支持单商户查询，不支持代理商管辖的多商户数据聚合。

## 修复方案

### 1. 修复MainChartController权限配置

**文件**：`sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MainChartController.java`

**修改内容**：
- 将所有权限从`ENT_MCH_MAIN_*`改为`ENT_C_MAIN_*`
- 添加`AgentMchRelationService`依赖
- 修改业务逻辑以支持多商户数据聚合
- 更新方法实现以适配代理商数据范围

**主要修改**：
```java
// 权限修改示例
@PreAuthorize("hasAuthority('ENT_C_MAIN_PAY_AMOUNT_WEEK')")  // 原：ENT_MCH_MAIN_PAY_AMOUNT_WEEK

// 业务逻辑修改示例
String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();
List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);
return ApiRes.ok(payOrderService.mainPageWeekCountByMchNos(mchNos));
```

### 2. 扩展PayOrderService支持多商户查询

**文件**：`service/src/main/java/com/unipay/service/impl/PayOrderService.java`

**新增方法**：
- `mainPageWeekCountByMchNos(List<String> mchNos)` - 支持多商户的周统计
- `mainPageNumCountByMchNos(List<String> mchNos)` - 支持多商户的数量统计
- `mainPagePayCountByMchNos(List<String> mchNos, String createdStart, String createdEnd)` - 支持多商户的交易统计
- `mainPagePayTypeCountByMchNos(List<String> mchNos, String createdStart, String createdEnd)` - 支持多商户的支付方式统计
- `payCountByMchNos(List<String> mchNos, ...)` - 支持多商户的基础统计
- `payTypeCountByMchNos(List<String> mchNos, ...)` - 支持多商户的支付方式基础统计

### 3. 扩展PayOrderMapper支持多商户查询

**文件**：`service/src/main/java/com/unipay/service/mapper/PayOrderMapper.java`

**新增方法**：
- `payCountByMchNos(Map param)` - 多商户支付统计
- `payTypeCountByMchNos(Map param)` - 多商户支付方式统计
- `selectOrderCountByMchNos(Map param)` - 多商户订单统计

**文件**：`service/src/main/java/com/unipay/service/mapper/PayOrderMapper.xml`

**新增SQL查询**：
```xml
<select id="payCountByMchNos" resultType="java.util.Map">
    SELECT COUNT(1) AS payCount, ROUND(IFNULL(SUM(amount) - SUM(refund_amount), 0)/100, 2) AS payAmount
    FROM t_pay_order
    WHERE 1=1
    <if test="mchNos != null and mchNos.size() > 0">
        AND mch_no IN
        <foreach collection="mchNos" item="mchNo" open="(" separator="," close=")">
            #{mchNo}
        </foreach>
    </if>
    <!-- 其他条件 -->
</select>
```

### 4. 完善权限配置

**文件**：
- `z-docs/sql/agent_permissions.sql`
- `z-docs/sql/init.sql`
- `z-docs/sql/patch.sql`

**新增权限**：
```sql
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_USER_INFO', '主页用户信息', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW());
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_USER_INFO');
```

### 5. 修复前端权限检查

**文件**：`unipay-web-ui/unipay-ui-agent/src/views/dashboard/Analysis.vue`

**问题**：前端Vue组件中使用的权限检查仍然是商户系统的权限（`ENT_MCH_MAIN_*`），导致权限检查失败，不会发起API请求。

**修改内容**：
```javascript
// 修改前
if ($access('ENT_MCH_MAIN_PAY_AMOUNT_WEEK')) {
if ($access('ENT_MCH_MAIN_NUMBER_COUNT')) {
if ($access('ENT_MCH_MAIN_PAY_COUNT')) {
if ($access('ENT_MCH_MAIN_PAY_TYPE_COUNT')) {
if ($access('ENT_MCH_MAIN_USER_INFO')) {

// 修改后
if ($access('ENT_C_MAIN_PAY_AMOUNT_WEEK')) {
if ($access('ENT_C_MAIN_NUMBER_COUNT')) {
if ($access('ENT_C_MAIN_PAY_COUNT')) {
if ($access('ENT_C_MAIN_PAY_TYPE_COUNT')) {
if ($access('ENT_C_MAIN_USER_INFO')) {
```

## 修复效果

修复后，代理商系统首页将能够：

1. **正常加载统计数据**：前端调用API后，后端能够正确响应并返回数据
2. **显示代理商范围数据**：只显示该代理商关联的商户的交易数据
3. **权限控制正确**：使用正确的代理商系统权限进行访问控制
4. **数据聚合准确**：将多个关联商户的数据进行正确聚合展示

## 测试验证

1. **编译验证**：后端代码编译成功，无语法错误
2. **权限验证**：确保所有必要的权限都已正确配置
3. **API路径验证**：前端调用路径与后端控制器路径一致
4. **前端验证**：前端开发服务器启动成功，权限检查已修复

## 部署说明

1. **数据库更新**：执行权限配置SQL脚本
2. **后端部署**：部署修复后的代理商系统后端代码
3. **前端部署**：部署修复后的代理商系统前端代码
4. **功能测试**：验证首页统计数据能够正常显示

## 测试步骤

1. **启动后端服务**：确保代理商系统后端服务正常运行（端口9219）
2. **启动前端服务**：运行 `npm run dev` 启动前端开发服务器（端口8003）
3. **登录测试**：使用代理商账号登录系统
4. **首页验证**：检查首页是否能正常显示统计数据
5. **权限验证**：确认只显示该代理商关联的商户数据

## 注意事项

1. 确保代理商与商户的关联关系数据正确配置
2. 验证代理商用户的权限分配正确
3. 测试不同代理商用户只能看到自己管辖范围内的数据

---

**修复完成时间**：2025-09-12  
**修复状态**：已完成，待部署测试
