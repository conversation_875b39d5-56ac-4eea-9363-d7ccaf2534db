package com.game.repository;

import com.game.entity.RechargeOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 充值订单数据访问层
 */
@Repository
public interface RechargeOrderRepository extends JpaRepository<RechargeOrder, Long> {

    /**
     * 根据游戏订单号查找订单
     */
    Optional<RechargeOrder> findByGameOrderNo(String gameOrderNo);

    /**
     * 根据支付网关订单号查找订单
     */
    Optional<RechargeOrder> findByPayOrderId(String payOrderId);

    /**
     * 根据用户ID查找订单列表
     */
    List<RechargeOrder> findByUserIdOrderByCreateTimeDesc(Long userId);

    /**
     * 根据用户名查找订单列表
     */
    List<RechargeOrder> findByUsernameOrderByCreateTimeDesc(String username);
}