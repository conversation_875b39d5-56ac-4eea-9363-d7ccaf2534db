#################################
# 开发环境通用配置文件(每个项目通用配置)
#################################
server:
  servlet:
    context-path: / #设置应用的目录.  前缀需要带/, 无需设置后缀, 示例 【 /xxx 】 or 【 / 】
spring:
  main:
    allow-circular-references: true
  servlet:
    multipart:
      enabled: true #是否启用http上传处理
      max-request-size: 10MB #最大请求文件的大小
      max-file-size: 10MB #设置单个文件最大长度
  freemarker:
    template-loader-path: classpath:/templates  #freemarker模板目录
    template-encoding: UTF-8
    suffix: .ftl
    settings:
      classic_compatible: true # 如果变量为null,转化为空字符串,比如做比较的时候按照空字符做比较
      number_format: '#' #数字格式进行原样显示，不加格式化字符例如  100,00
  datasource:
    # yml填写url连接串， 无需将&符号进行转义
    url: ********************************************************************************************************************************************************************************
    username: root
    password: LHX.0618
    druid:
      # 连接池配置项
      initial-size: 5 #初始化时建立物理连接的个数
      min-idle: 5 #最小连接池数量
      max-active: 30 #最大连接池数量
      max-wait: 60000 #获取连接时最大等待时间，单位毫秒
      # 检测相关
      test-while-idle: true # 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-on-borrow: false # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-return: false # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      time-between-eviction-runs-millis: 60000 #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      min-evictable-idle-time-millis: 300000 #连接保持空闲而不被驱逐的最小时间
      validation-query: SELECT 1 FROM DUAL
      # 是否缓存preparedStatement
      pool-prepared-statements: false # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
      max-pool-prepared-statement-per-connection-size: 20 # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  cache:
    type: redis
  data:
    redis:
      host: *************
      port: 6379
      timeout: 10000
      database: 0
      password: LHX.0618

# #activeMQ配置 ( 注意： activeMQ配置项需在spring的下级 )
  activemq:
    broker-url: failover:(tcp://*************:61616?wireFormat.maxInactivityDuration=0)  #连接地址
    in-memory: false # Jeepay项目不可使用内存模式， 需要连接多个消费者。
    user: system # activeMQ默认无需账密认证。 打开认证：activemq.xml添加simpleAuthenticationPlugin标签，账密在credentials.properties文件。
    password: manager
    pool:
      enabled: true
      max-connections: 10
      idle-timeout: 30000  # 空闲的连接过期时间，默认为30秒

  #日志配置参数。
  # 当存在logback-spring.xml文件时： 该配置将引进到logback配置，  springboot配置不生效。
  # 不存在logback-spring.xml 文件时， 使用springboot的配置， 同样可用。
  web:
    resources:
      static-locations: classpath:/static
logging:
  level:
    root: info   #主日志级别
    com.unipay: debug   #该项目日志级别，当需要打印sql时请开启为debug
  file:
    path: ./logs  #日志存放地址

#系统业务参数
isys:
  #是否允许跨域请求 [生产环境建议关闭， 若api与前端项目没有在同一个域名下时，应开启此配置或在nginx统一配置允许跨域]
  allow-cors: true

  #是否内存缓存配置信息: true表示开启如支付网关地址/商户应用配置/服务商配置等， 开启后需检查MQ的广播模式是否正常； false表示直接查询DB.
  cache-config: true

  oss:
    file-root-path: /home/<USER>/upload #存储根路径 ( 无需以‘/’结尾 )
    file-public-path: ${isys.oss.file-root-path}/public #公共读取块  ( 一般配合root-path参数进行设置，需以‘/’ 开头, 无需以‘/’结尾 )
    file-private-path: ${isys.oss.file-root-path}/private #私有化本地访问，不允许url方式公共读取 ( 一般配合root-path参数进行设置，需以‘/’ 开头, 无需以‘/’结尾  )

    # [local]: 本地存储，所有的文件将存在放本地，可通过nfs, rsync工具实现多机共享；
    # [aliyun-oss]: 将文件统一上传到阿里云oss服务器;  注意：需调整jeepay-components-oss/pom.xml中的aliyun-sdk-oss组件依赖方式
    service-type: local

    # 阿里云OSS服务配置信息
    aliyun-oss:
      endpoint: oss-cn-beijing.aliyuncs.com  #endpoint  如： oss-cn-beijing.aliyuncs.com
      public-bucket-name: bucket1 #公共读 桶名称
      private-bucket-name: bucket2  #私有 桶名称
      access-key-id: KEY_KEY_KEY   #AccessKeyId
      access-key-secret: SECRET_SECRET_SECRET  #AccessKeySecret

  mq:
    vender: activeMQ  #  切换MQ厂商， 支持：【 activeMQ  rabbitMQ  rocketMQ  aliYunRocketMQ 】， 需正确配置 【对应的yml参数】 和 【jeepay-components-mq项目下pom.xml中的依赖包】。

