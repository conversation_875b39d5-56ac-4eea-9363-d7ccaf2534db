
package com.unipay.agent.ctrl.merchant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unipay.core.entity.MchInfo;
import com.unipay.core.entity.SysUser;
import com.unipay.core.model.ApiRes;
import com.unipay.agent.ctrl.CommonCtrl;
import com.unipay.service.impl.AgentMchRelationService;
import com.unipay.service.impl.AgentInfoService;
import com.unipay.service.impl.MchInfoService;
import com.unipay.service.impl.PayOrderService;
import com.unipay.service.impl.SysUserService;
import com.unipay.core.entity.AgentInfo;
import com.unipay.core.constants.CS;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代理商系统主页数据统计控制器
 *
 * <AUTHOR>
 * @date 2021-04-27 15:50
 */
@Tag(name = "代理商主页统计")
@Slf4j
@RestController
@RequestMapping("api/mainChart")
public class MainChartController extends CommonCtrl {

    @Autowired private PayOrderService payOrderService;

    @Autowired private SysUserService sysUserService;

    @Autowired private MchInfoService mchInfoService;

    @Autowired private AgentMchRelationService agentMchRelationService;

    @Autowired private AgentInfoService agentInfoService;


    /** 周交易总金额 */
    @Operation(summary = "代理商周交易总金额",description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER)
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_PAY_AMOUNT_WEEK')")
    @RequestMapping(value="/payAmountWeek", method = RequestMethod.GET)
    public ApiRes payAmountWeek() {
        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        // 获取代理商关联的商户号列表
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);

        if (mchNos.isEmpty()) {
            // 如果没有关联商户，返回空数据
            return ApiRes.ok(new ArrayList<>());
        }

        return ApiRes.ok(payOrderService.mainPageWeekCountByMchNos(mchNos));
    }

    /**
     * 代理商数量统计：关联商户数量、总交易金额、总交易笔数
     * @return
     */
    @Operation(summary = "代理商数量统计：关联商户数量、总交易金额、总交易笔数", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER)
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_NUMBER_COUNT')")
    @RequestMapping(value="/numCount", method = RequestMethod.GET)
    public ApiRes numCount() {
        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        // 获取代理商关联的商户号列表
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);

        if (mchNos.isEmpty()) {
            // 如果没有关联商户，返回空数据
            return ApiRes.ok(payOrderService.getEmptyNumCountData());
        }

        return ApiRes.ok(payOrderService.mainPageNumCountByMchNos(mchNos));
    }

    /**
     * 代理商和商户数量统计：下级代理商数量、关联商户数量
     * @return
     */
    @Operation(summary = "代理商和商户数量统计：下级代理商数量、关联商户数量", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER)
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_AGENT_MCH_COUNT')")
    @RequestMapping(value="/agentMchCount", method = RequestMethod.GET)
    public ApiRes agentMchCount() {
        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        Map<String, Object> result = new HashMap<>();

        // 统计下级代理商数量（直属下级）
        long subAgentCount = agentInfoService.count(
                AgentInfo.gw().eq(AgentInfo::getParentAgentNo, currentAgentNo)
                        .eq(AgentInfo::getState, AgentInfo.STATE_NORMAL));
        result.put("subAgentCount", subAgentCount);

        // 统计关联商户数量
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);
        long mchCount = 0;
        if (!mchNos.isEmpty()) {
            mchCount = mchInfoService.count(
                    MchInfo.gw().in(MchInfo::getMchNo, mchNos)
                            .eq(MchInfo::getState, CS.PUB_USABLE));
        }
        result.put("mchCount", mchCount);

        return ApiRes.ok(result);
    }

    /** 代理商交易统计 */
    @Operation(summary = "代理商交易统计", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "createdStart", description = "日期格式字符串（yyyy-MM-dd），时间范围查询--开始时间，须和结束时间一起使用，否则默认查最近七天（含今天）"),
            @Parameter(name = "createdEnd", description = "日期格式字符串（yyyy-MM-dd），时间范围查询--结束时间，须和开始时间一起使用，否则默认查最近七天（含今天）")
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_PAY_COUNT')")
    @RequestMapping(value="/payCount", method = RequestMethod.GET)
    public ApiRes<List<Map>> payCount() {
        // 获取传入参数
        JSONObject paramJSON = getReqParamJSON();
        String createdStart = paramJSON.getString("createdStart");
        String createdEnd = paramJSON.getString("createdEnd");

        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        // 获取代理商关联的商户号列表
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);

        if (mchNos.isEmpty()) {
            // 如果没有关联商户，返回空数据
            return ApiRes.ok(new ArrayList<>());
        }

        List<Map> mapList = payOrderService.mainPagePayCountByMchNos(mchNos, createdStart, createdEnd);
        //返回数据
        return ApiRes.ok(mapList);
    }

    /** 代理商支付方式统计 */
    @Operation(summary = "代理商支付方式统计", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "createdStart", description = "日期格式字符串（yyyy-MM-dd），时间范围查询--开始时间，须和结束时间一起使用，否则默认查最近七天（含今天）"),
            @Parameter(name = "createdEnd", description = "日期格式字符串（yyyy-MM-dd），时间范围查询--结束时间，须和开始时间一起使用，否则默认查最近七天（含今天）")
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_PAY_TYPE_COUNT')")
    @RequestMapping(value="/payTypeCount", method = RequestMethod.GET)
    public ApiRes<ArrayList> payWayCount() {
        JSONObject paramJSON = getReqParamJSON();
        // 开始、结束时间
        String createdStart = paramJSON.getString("createdStart");
        String createdEnd = paramJSON.getString("createdEnd");

        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        // 获取代理商关联的商户号列表
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);

        if (mchNos.isEmpty()) {
            // 如果没有关联商户，返回空数据
            return ApiRes.ok(new ArrayList<>());
        }

        ArrayList arrayResult = payOrderService.mainPagePayTypeCountByMchNos(mchNos, createdStart, createdEnd);
        return ApiRes.ok(arrayResult);
    }

    /** 代理商基本信息、用户基本信息 **/
    @Operation(summary = "代理商基本信息", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
    })
    @PreAuthorize("hasAuthority('ENT_C_MAIN_USER_INFO')")
    @RequestMapping(value="", method = RequestMethod.GET)
    public ApiRes userDetail() {
        SysUser sysUser = sysUserService.getById(getCurrentUser().getSysUser().getSysUserId());

        // 获取当前登录用户的代理商号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();

        // 构建返回数据
        JSONObject json = new JSONObject();
        json.put("agentNo", currentAgentNo);
        json.put("loginUsername", sysUser.getLoginUsername());
        json.put("realname", sysUser.getRealname());
        json.put("userType", "AGENT");

        return ApiRes.ok(json);
    }
}
